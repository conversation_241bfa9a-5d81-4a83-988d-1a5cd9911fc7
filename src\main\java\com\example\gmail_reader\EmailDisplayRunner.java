package com.example.gmailreader;

import com.example.gmailreader.dto.EmailData;
import com.example.gmailreader.service.GmailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class EmailDisplayRunner implements CommandLineRunner {

    @Autowired
    private GmailService gmailService;

    @Override
    public void run(String... args) throws Exception {
        System.out.println("Fetching last 200 emails...");

        List<EmailData> emails = gmailService.getEmails();

        if (emails.isEmpty()) {
            System.out.println("No emails to display.");
            return;
        }
        
        System.out.println("-----------------------------------------------------");
        System.out.println("               LATEST EMAILS                         ");
        System.out.println("-----------------------------------------------------");
        
        for (int i = 0; i < emails.size(); i++) {
            EmailData email = emails.get(i);
            System.out.printf("Email #%d\n", i + 1);
            System.out.printf("  From: %s\n", email.getFrom());
            System.out.printf("  Subject: %s\n", email.getSubject());
            System.out.println("-----------------------------------------------------");
        }
    }
}