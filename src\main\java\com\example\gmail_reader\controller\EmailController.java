package com.example.gmailreader.controller;

import com.example.gmailreader.dto.EmailData;
import com.example.gmailreader.service.GmailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.List;

@RestController
public class EmailController {

    @Autowired
    private GmailService gmailService;

    @GetMapping("/emails")
    public ResponseEntity<?> getMyLast200Emails() {
        try {
            List<EmailData> emails = gmailService.getEmails();
            return ResponseEntity.ok(emails);
        } catch (GeneralSecurityException | IOException e) {
            e.printStackTrace();
            // In a real app, you'd have better error handling
            return ResponseEntity.status(500).body("Error fetching emails: " + e.getMessage());
        }
    }
}